{% extends 'index.html' %}
{% load static %}
{% block content %}
<head>
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <style>
        .loss-amount{
            color: rgb(224, 20, 20) !important;
        }
        .nor-amount{
            color: grey !important;
        }
        .profit-amount {
            color: #28a745 !important;
        }
        .profit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .btn-small {
            background-color: #007bff;
            color: white;
            padding: 6px 12px;
            font-size: 0.875rem;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-small i {
            font-size: 0.9rem;
        }

        .amount-usd {
            font-size: 0.85rem;
            color: #666;
            margin-top: 2px;
        }

        .exchange-rate-info {
            font-size: 0.85rem;
            color: #666;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f5f5f5;
            padding: 8px 15px;
            border-radius: 4px;
        }

        .exchange-rate-info a {
            margin-left: 15px;
        }

        /* Dashboard summary layout */
        .dashboard-summary {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            flex: 1 1 calc(25% - 20px);
            min-width: 220px;
            max-width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            align-items: center;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background-color: #f0f7ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .card-content {
            flex-grow: 1;
            min-width: 0;
        }

        .card-content h3 {
            font-size: 1rem;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Monthly report selector styles */
        .report-selector {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .report-selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .report-selector-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .report-selector-form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .report-selector-form select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            background-color: white;
        }

        .report-selector-form button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .report-selector-form button:hover {
            background-color: #0069d9;
        }

        .report-badge {
            display: inline-block;
            padding: 4px 8px;
            background-color: #17a2b8;
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .report-quick-links {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .report-quick-link {
            padding: 5px 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 0.85rem;
            color: #495057;
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .report-quick-link:hover {
            background-color: #dee2e6;
            color: #212529;
        }
    </style>
</head>
<div class="finance-dashboard">
    <div class="dashboard-header">
        <h1>Finance Overview</h1>
        <h2>{{ project.name }}</h2>
        {% if is_historical_report %}
        <div class="alert alert-info">
            <strong>Viewing Historical Report:</strong> {{ report_month }}/{{ report_year }}
            <a href="{% url 'finance_dashboard' project.id %}" class="btn-small" style="margin-left: 15px;">
                <i class="fas fa-sync"></i> View Current Data
            </a>
        </div>
        {% endif %}

        <!-- Monthly Report Selector -->
        <div class="report-selector">
            <div class="report-selector-header">
                <h3 class="report-selector-title">
                    {% if is_historical_report %}Historical Report{% else %}Current Data{% endif %}
                    {% if is_historical_report %}<span class="report-badge">{{ report_month }}/{{ report_year }}</span>{% endif %}
                </h3>
            </div>
            {% if messages %}
            <div class="alert alert-info" style="margin-bottom: 15px;">
                {% for message in messages %}
                    {{ message }}
                {% endfor %}
            </div>
            {% endif %}

            <form class="report-selector-form" method="get" action="{% url 'finance_dashboard' project.id %}">
                <div>
                    <label for="month">Month:</label>
                    {% now "n" as current_month_num %}
                    <select name="month" id="month">
                        <option value="1" {% if report_month == 1 %}selected{% elif not report_month and current_month_num == '1' %}selected{% endif %}>January</option>
                        <option value="2" {% if report_month == 2 %}selected{% elif not report_month and current_month_num == '2' %}selected{% endif %}>February</option>
                        <option value="3" {% if report_month == 3 %}selected{% elif not report_month and current_month_num == '3' %}selected{% endif %}>March</option>
                        <option value="4" {% if report_month == 4 %}selected{% elif not report_month and current_month_num == '4' %}selected{% endif %}>April</option>
                        <option value="5" {% if report_month == 5 %}selected{% elif not report_month and current_month_num == '5' %}selected{% endif %}>May</option>
                        <option value="6" {% if report_month == 6 %}selected{% elif not report_month and current_month_num == '6' %}selected{% endif %}>June</option>
                        <option value="7" {% if report_month == 7 %}selected{% elif not report_month and current_month_num == '7' %}selected{% endif %}>July</option>
                        <option value="8" {% if report_month == 8 %}selected{% elif not report_month and current_month_num == '8' %}selected{% endif %}>August</option>
                        <option value="9" {% if report_month == 9 %}selected{% elif not report_month and current_month_num == '9' %}selected{% endif %}>September</option>
                        <option value="10" {% if report_month == 10 %}selected{% elif not report_month and current_month_num == '10' %}selected{% endif %}>October</option>
                        <option value="11" {% if report_month == 11 %}selected{% elif not report_month and current_month_num == '11' %}selected{% endif %}>November</option>
                        <option value="12" {% if report_month == 12 %}selected{% elif not report_month and current_month_num == '12' %}selected{% endif %}>December</option>
                    </select>
                </div>

                <div>
                    <label for="year">Year:</label>
                    <select name="year" id="year">
                        {% now "Y" as current_year %}
                        {% with current_year_int=current_year|add:"0" %}
                            {% for i in "0123456789" %}
                                {% with offset="-"|add:i %}
                                    {% with year=current_year_int|add:offset %}
                                        <option value="{{ year }}" {% if report_year == year %}selected{% endif %}>{{ year }}</option>
                                    {% endwith %}
                                {% endwith %}
                            {% endfor %}
                            <option value="{{ current_year_int|add:"-10" }}" {% if report_year == current_year_int|add:"-10" %}selected{% endif %}>{{ current_year_int|add:"-10" }}</option>
                        {% endwith %}
                    </select>
                </div>

                <button type="submit">View Report</button>
            </form>

            {% if available_reports %}
            <div class="report-quick-links">
                <span>Quick access:</span>
                {% for report in available_reports %}
                <a href="{% url 'finance_dashboard' project.id %}?month={{ report.month }}&year={{ report.year }}" class="report-quick-link">
                    {{ report.get_month_display }} {{ report.year }}
                </a>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        {% if exchange_rate %}
        <div class="exchange-rate-info">
            <p>Exchange Rate: 1 USD = {{ exchange_rate }} INR (as of {{ exchange_rate_date }})</p>
            <a href="{% url 'exchange_rates' %}" class="btn-small">
                <i class="bi bi-currency-exchange"></i> View Exchange Rates
            </a>
        </div>
        {% endif %}
    </div>
    <div class="dashboard-summary">
        <div class="summary-card estimated-cost">
            <div class="card-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="card-content">
                <h3>Estimated<br>Project Cost</h3>
                <p class="amount">₹{{ estimated_cost|floatformat:2 }}</p>
                {% if show_usd %}
                <p class="amount-usd">${{ estimated_cost_usd|floatformat:2 }} USD</p>
                {% endif %}
            </div>
        </div>
        <div class="summary-card employee-cost">
            <div class="card-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="card-content">
                <h3>Total Employee Cost</h3>
                <p class="amount">₹{{ total_employee_cost|floatformat:2 }}</p>
                {% if show_usd %}
                <p class="amount-usd">${{ total_employee_cost_usd|floatformat:2 }} USD</p>
                {% endif %}
            </div>
        </div>        <div class="summary-card operational-cost">
            <div class="card-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="card-content">
                <h3>Operational Cost</h3>
                <p class="amount">₹{{ operational_cost|floatformat:2 }}</p>
                {% if show_usd %}
                <p class="amount-usd">${{ operational_cost_usd|floatformat:2 }} USD</p>
                {% endif %}
            </div>
        </div>
        <div class="summary-card profit">
            <div class="card-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="card-content">
                <!-- profit variable is being used to print both profit and loss -->
                {% if profit > 0 %}
                    <h3>Remaining<br>Operational Cost</h3>
                    <p class="amount profit-amount">₹{{ profit|floatformat:2 }}</p>
                    {% if show_usd %}
                    <p class="amount-usd profit-amount">${{ profit_usd|floatformat:2 }} USD</p>
                    {% endif %}
                {% elif profit == 0 %}
                    <h3>Not Configured</h3>
                    <p class="amount nor-amount">₹{{ profit|floatformat:2 }}</p>
                    {% if show_usd %}
                    <p class="amount-usd nor-amount">${{ profit_usd|floatformat:2 }} USD</p>
                    {% endif %}
                {% else %}
                    <h3>Loss</h3>
                    <p class="amount loss-amount">₹{{ profit|floatformat:2 }}</p>
                    {% if show_usd %}
                    <p class="amount-usd loss-amount">${{ profit_usd|floatformat:2 }} USD</p>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    <div class="dashboard-section">
        <div class="section-header profit-header">
            <h2>Employee Cost Allocation</h2>
            <div class="section-actions">
                <a href="{% url 'finance_project_edit' project.id %}" class="btn-small">
                    <i class="fas fa-users-cog"></i> Manage
                </a>
            </div>
        </div>
        <div class="table-container">
            <table class="data-table">                <thead>
                    <tr>
                        <th>Employee</th>
                        <th>Monthly Salary (INR)</th>
                        {% if show_usd %}<th>Monthly Salary (USD)</th>{% endif %}
                        <th>Allocation (%)</th>
                        <th>Allocated Cost (INR)</th>
                        {% if show_usd %}<th>Allocated Cost (USD)</th>{% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for allocation in employee_allocations %}
                    <tr>
                        <td>{{ allocation.employee.employee_first_name }} {{ allocation.employee.employee_last_name }}</td>
                        <td class="amount-cell">₹{{ allocation.monthly_salary|floatformat:2 }}</td>
                        {% if show_usd %}<td class="amount-cell">${{ allocation.monthly_salary_usd|floatformat:2 }}</td>{% endif %}
                        <td class="percentage-cell">{{ allocation.percentage_allocation }}%</td>
                        <td class="amount-cell">₹{{ allocation.allocated_cost|floatformat:2 }}</td>
                        {% if show_usd %}<td class="amount-cell">${{ allocation.allocated_cost_usd|floatformat:2 }}</td>{% endif %}
                    </tr>                    {% empty %}
                    <tr class="empty-row">
                        <td colspan="{% if show_usd %}6{% else %}4{% endif %}">No employees assigned to this project.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="dashboard-section">
        <div class="section-header profit-header">
            <h2>Dynamic Cost Distribution</h2>
            <div class="section-actions">
                <a href="{% url 'finance_project_edit' project.id %}" class="btn-small">
                    <i class="fas fa-cogs"></i> Manage
                </a>
            </div>
        </div>
        <div class="table-container">
            <table class="data-table">                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Percentage</th>
                        <th>Amount (INR)</th>
                        {% if show_usd %}<th>Amount (USD)</th>{% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for cost in calculated_costs %}
                    <tr>
                        <td>{{ cost.category }}</td>
                        <td class="percentage-cell">{{ cost.percentage }}%</td>
                        <td class="amount-cell">₹{{ cost.calculated_value|floatformat:2 }}</td>
                        {% if show_usd %}<td class="amount-cell">${{ cost.calculated_value_usd|floatformat:2 }}</td>{% endif %}
                    </tr>
                    {% empty %}
                    <tr class="empty-row">
                        <td colspan="{% if show_usd %}4{% else %}3{% endif %}">No cost distribution set yet.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="dashboard-section">
        <div class="section-header profit-header">
            <h2>All Extra Operational Costs</h2>
            <a href="{% url 'add_additional_cost' project.id %}" class="btn-small">
                <i class="fas fa-money-bill-wave"></i> Manage
            </a>
        </div>

        <div class="table-container">
            <table class="data-table">                <thead>
                    <tr>
                        <th>Cost Name</th>
                        <th>Amount (USD)</th>
                        {% if show_usd %}<th>Amount (INR)</th>{% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for cost in additional_costs %}
                    <tr>
                        <td>{{ cost.original.cost_name }}</td>
                        <td class="amount-cell">${{ cost.original.cost_value|floatformat:2 }}</td>
                        {% if show_usd %}<td class="amount-cell">₹{{ cost.cost_value_usd|floatformat:2 }}</td>{% endif %}
                    </tr>
                    {% empty %}
                    <tr class="empty-row">
                        <td colspan="{% if show_usd %}3{% else %}2{% endif %}">No additional costs recorded.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="dashboard-section">
        <div class="section-header profit-header">
            <h2>Profit Distribution</h2>
            <a href="{% url 'profit_distribution_view' project.id %}" class="btn-small">
                <i class="fas fa-money-bill-wave"></i> Manage
            </a>
        </div>

        <div class="table-container">
            <table class="data-table">                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Percentage</th>
                        <th>Allocated Profit (INR)</th>
                        {% if show_usd %}<th>Allocated Profit (USD)</th>{% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for distribution in distributed_profits %}
                    <tr>
                        <td>{{ distribution.category }}</td>
                        <td class="percentage-cell">{{ distribution.percentage }}%</td>
                        <td class="amount-cell">₹{{ distribution.allocated_profit|floatformat:2 }}</td>
                        {% if show_usd %}<td class="amount-cell">${{ distribution.allocated_profit_usd|floatformat:2 }}</td>{% endif %}
                    </tr>
                    {% empty %}
                    <tr class="empty-row">
                        <td colspan="{% if show_usd %}4{% else %}3{% endif %}">No profit distribution set yet.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

</div>
{% endblock %}