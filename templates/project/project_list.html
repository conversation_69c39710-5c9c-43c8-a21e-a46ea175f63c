{% extends 'index.html' %}
{% block content %}
{% load static %}
{% load i18n %}
{% include 'filter_tags.html' %}

<head>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <!-- Bootstrap 5 JS Bundle with <PERSON><PERSON> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        /* Custom styles for action buttons */
        .action-btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px; /* Space between buttons */
        }

        .action-btn {
            margin: 4px 0; /* Vertical spacing when stacked */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .action-btn-group {
                gap: 10px; /* Increased spacing on mobile */
                justify-content: center; /* Center buttons on mobile */
            }

            .action-btn {
                margin: 5px 0; /* More vertical spacing on mobile */
                padding: 8px 12px; /* Larger touch target */
                min-width: 90px; /* Minimum width for better touch targets */
            }

            /* Make table more mobile-friendly */
            .table-responsive {
                overflow-x: auto;
            }

            /* Improve form elements on mobile */
            .form-control, .form-select {
                margin-bottom: 8px;
            }
        }
    </style>
</head>
<div class="container mt-5">
    <div class="card horilla-card">
        <div class="card-header horilla-header d-flex flex-wrap justify-content-between align-items-center">
            <h3 class="card-title mb-3 mb-md-0 fw-bold">{% trans "Projects" %}</h3>
            <div class="d-flex flex-wrap">
                <!-- Search Form -->
                <form method="GET" action="{% url 'project:project_list' %}" class="d-flex me-3 mb-2">
                    <input type="hidden" name="owner" value="{{ selected_owner }}">
                    <div class="d-flex me-2">
                        <input type="text" name="q" value="{{ query }}" class="form-control horilla-search me-2"
                            placeholder="{% trans 'Search projects...' %}">
                        <button type="submit" class="btn btn-outline-primary action-btn">
                            <i class="fas fa-search"></i> {% trans "Search" %}
                        </button>
                    </div>
                </form>

                <!-- Filter by Owner Form -->
                <form method="GET" action="{% url 'project:project_list' %}" class="d-flex me-3 mb-2">
                    <input type="hidden" name="q" value="{{ query }}">
                    <div class="d-flex me-2">
                        <select name="owner" class="form-select horilla-select" style="min-width: 250px;" onchange="this.form.submit()">
                            <option value="">{% trans "All Owners" %}</option>
                            {% for owner in all_owners %}
                                <option value="{{ owner.id }}" {% if selected_owner == owner.id|stringformat:"i" %}selected{% endif %}>
                                    {{ owner.get_full_name|default:owner }}
                                </option>
                            {% endfor %}
                        </select>
                        <!-- Filter button removed, but functionality preserved with onchange event -->
                    </div>
                </form>

                <div class="d-flex flex-wrap mb-2">
                    {% if qb_connected %}
                        <button id="syncQbCustomersBtn" class="btn btn-primary action-btn me-2 mb-2">
                            <i class="fas fa-sync"></i> {% trans "Sync QuickBooks Customers" %}
                            <span id="syncLoader" class="spinner-border spinner-border-sm ms-1" role="status" style="display: none;"></span>
                        </button>
                    {% else %}
                        <a href="{% url 'quickbooks_connect' %}" class="btn btn-outline-primary action-btn me-2 mb-2">
                            <i class="fas fa-link"></i> {% trans "Connect to QuickBooks" %}
                        </a>
                    {% endif %}

                {% if is_admin or is_director or is_project_owner %}
                <a href="{% url 'project:project_create' %}" class="btn horilla-btn action-btn mb-2">
                    <i class="fas fa-plus"></i> {% trans "Add Project" %}
                </a>
                {% endif %}
            </div>
        </div>

        <div class="card-body p-4">
            <div class="table-responsive">
                <table class="table {% if projects %}table-hover{% endif %} align-middle text-center horilla-table">
                    <thead>
                        <tr>
                            <th class="text-start px-4 py-3">{% trans "Project Name" %}</th>
                            <th class="py-3">{% trans "Status" %}</th>
                            <th class="py-3">{% trans "Owner" %}</th>
                            <th class="py-3">{% trans "Estimated Cost (USD)" %}</th>
                            <th class="py-3">{% trans "Open Balance (USD)" %}</th>
                            <th class="py-3">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in projects %}
                        <tr class="border-bottom">
                            <td class="text-start px-4 fw-medium py-3">{{ project.name }}</td>
                            <td class="py-3">
                                {% if project.status == 'In Progress' %}
                                    <span class="badge bg-success text-white px-3 py-2">{{ project.get_status_display }}</span>
                                {% elif project.status == 'Planning' %}
                                    <span class="badge bg-warning text-dark px-3 py-2">{{ project.get_status_display }}</span>
                                {% else %}
                                    <span class="badge bg-secondary text-white px-3 py-2">{{ project.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td class="py-3">{{ project.project_owner }}</td>
                            <td class="fw-bold text-primary py-3">{% if project.encrypted_estimated_cost %}${{ project.encrypted_estimated_cost|floatformat:2 }}{% else %}-{% endif %}</td>
                            <td class="fw-bold text-primary py-3">{% if project.encrypted_open_balance %}${{ project.encrypted_open_balance|floatformat:2 }}{% else %}-{% endif %}</td>
                            <td class="py-3">
                                <div class="action-btn-group">
                                    <a href="{% url 'project:project_detail' project.pk %}" class="btn btn-sm btn-outline-info action-btn">
                                        <i class="fas fa-eye"></i> {% trans "View" %}
                                    </a>

                                    <a href="{% url 'project:project_update' project.pk %}" class="btn btn-sm btn-outline-warning action-btn">
                                        <i class="fas fa-edit"></i> {% trans "Edit" %}
                                    </a>

                                    <a href="{% url 'project:project_delete' project.pk %}" class="btn btn-sm btn-outline-danger action-btn">
                                        <i class="fas fa-trash"></i> {% trans "Delete" %}
                                    </a>

                                    {% if project.customer_id and qb_connected %}
                                    <button class="btn btn-sm btn-outline-primary sync-project-btn action-btn" data-project-id="{{ project.pk }}">
                                        <i class="fas fa-sync"></i> {% trans "Refresh" %}
                                        <span class="project-sync-loader spinner-border spinner-border-sm ms-1" role="status" style="display: none;"></span>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center text-muted py-4">
                                {% if query %}
                                    {% trans "No projects found matching your search." %}
                                {% else %}
                                    {% trans "No projects found." %}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Toast for notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="syncToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toastTitle">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Hello, world! This is a toast message.
        </div>
    </div>
</div>

<!-- JavaScript for Sync Buttons -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const syncBtn = document.getElementById('syncQbCustomersBtn');
        const syncLoader = document.getElementById('syncLoader');
        const syncToast = document.getElementById('syncToast');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');

        // Initialize Bootstrap toast
        const toast = new bootstrap.Toast(syncToast);

        // Function to show toast
        function showToast(status, message) {
            if (status === 'success') {
                toastTitle.textContent = 'Success';
                toastTitle.className = 'me-auto text-success';
            } else if (status === 'info') {
                toastTitle.textContent = 'Info';
                toastTitle.className = 'me-auto text-info';
            } else if (status === 'warning') {
                toastTitle.textContent = 'Warning';
                toastTitle.className = 'me-auto text-warning';
            } else {
                toastTitle.textContent = 'Error';
                toastTitle.className = 'me-auto text-danger';
            }

            toastMessage.textContent = message;
            toast.show();
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Handle sync all customers button
        if (syncBtn) {
            syncBtn.addEventListener('click', function() {
                // Show loader
                syncLoader.style.display = 'inline-block';
                syncBtn.disabled = true;

                // Make AJAX request to sync customers
                fetch('{% url "project:sync_qb_customers" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loader
                    syncLoader.style.display = 'none';
                    syncBtn.disabled = false;

                    // Show toast with result
                    showToast(data.status, data.message);

                    // If new projects were created or updated, reload the page after 2 seconds
                    if (data.status === 'success' && (data.created_count > 0 || data.updated_count > 0)) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                })
                .catch(error => {
                    // Hide loader
                    syncLoader.style.display = 'none';
                    syncBtn.disabled = false;

                    // Show error toast
                    showToast('error', 'Director level permissions are required to sync projects.');
                    console.error('Error:', error);
                });
            });
        }

        // Handle individual project sync buttons
        const syncProjectBtns = document.querySelectorAll('.sync-project-btn');
        syncProjectBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const projectId = this.getAttribute('data-project-id');
                const loader = this.querySelector('.project-sync-loader');

                // Show loader
                loader.style.display = 'inline-block';
                this.disabled = true;

                // Make AJAX request to sync single project
                fetch(`/project/${projectId}/sync-qb-customer/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loader
                    loader.style.display = 'none';
                    this.disabled = false;

                    // Show toast with result
                    showToast(data.status, data.message);

                    // If project was updated, update the row
                    if (data.status === 'success') {
                        // Find the row and update the name and encrypted_open_balance
                        const row = this.closest('tr');
                        const nameCell = row.querySelector('td:first-child');
                        const balanceCell = row.querySelector('td:nth-child(5)');

                        if (nameCell && data.name) {
                            nameCell.textContent = data.name;
                        }

                        if (balanceCell && data.encrypted_open_balance !== undefined) {
                            balanceCell.textContent = '$' + parseFloat(data.encrypted_open_balance).toFixed(2);
                        }
                    }
                })
                .catch(error => {
                    // Hide loader
                    loader.style.display = 'none';
                    this.disabled = false;

                    // Show error toast
                    showToast('error', 'Director level permissions are required to sync projects.');
                    console.error('Error:', error);
                });
            });
        });
    });
</script>

{% endblock %}