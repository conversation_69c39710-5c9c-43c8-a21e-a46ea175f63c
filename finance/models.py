from django.db import models
from decimal import Decimal
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from base.encryption import EncryptedDecimal<PERSON>ield
from project.models import Project
from employee.models import Employee

class Finance(models.Model):
    project = models.OneToOneField(Project, on_delete=models.CASCADE, related_name="finance")

    def __str__(self):
        return f"Finance for {self.project.name}"

class EmployeeAllocation(models.Model):
    finance = models.ForeignKey(Finance, on_delete=models.CASCADE, related_name="employee_allocations")
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name="employee_allocations")
    percentage_allocation = models.FloatField(default=0.0)
    allocated_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)

    def __str__(self):
        return f"{self.employee} - {self.percentage_allocation}%"

class CostDistribution(models.Model):
    project = models.ForeignKey(Project, related_name='distributions', on_delete=models.CASCADE)
    category = models.CharField(max_length=255)
    percentage = models.FloatField()

    def __str__(self):
        return f"{self.category}: {self.percentage}%"

class ProfitDistribution(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    category = models.CharField(max_length=100)
    percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    def allocated_profit(self, total_profit):
        """Calculate allocated profit based on percentage"""
        return (Decimal(self.percentage) / Decimal(100)) * total_profit

    def __str__(self):
        return f"{self.category} - {self.percentage}%"

class AdditionalCost(models.Model):
    """
    Model to store additional costs for projects.
    Note: cost_value should be entered in USD to maintain consistency with project estimated costs.
    """
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="additional_costs")
    cost_name = models.CharField(max_length=255)
    cost_value = models.DecimalField(max_digits=10, decimal_places=2, help_text="Cost value in USD")

    def __str__(self):
        return f"{self.cost_name}: ${self.cost_value}"

class ExchangeRate(models.Model):
    """
    Model to store currency exchange rates
    """
    base_currency = models.CharField(max_length=3, default='USD')
    target_currency = models.CharField(max_length=3, default='INR')
    rate = models.DecimalField(max_digits=10, decimal_places=6)
    date = models.DateField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Exchange Rate'
        verbose_name_plural = 'Exchange Rates'
        ordering = ['-date']

    def __str__(self):
        return f"{self.base_currency}/{self.target_currency}: {self.rate} on {self.date}"


class MonthlyFinanceReport(models.Model):
    """
    Model to store monthly finance report data for projects
    """
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='finance_monthly_reports')
    year = models.PositiveIntegerField()
    month = models.PositiveIntegerField(choices=[
        (1, _('January')),
        (2, _('February')),
        (3, _('March')),
        (4, _('April')),
        (5, _('May')),
        (6, _('June')),
        (7, _('July')),
        (8, _('August')),
        (9, _('September')),
        (10, _('October')),
        (11, _('November')),
        (12, _('December')),
    ])

    # Exchange rate data
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    exchange_rate_date = models.DateField(null=True, blank=True)

    # Encrypted financial data
    encrypted_estimated_cost = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_estimated_cost_usd = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_total_employee_cost = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_total_employee_cost_usd = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_operational_cost = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_operational_cost_usd = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_profit = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    encrypted_profit_usd = EncryptedDecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # JSON fields to store detailed data
    employee_allocations_json = models.JSONField(default=dict, blank=True)
    cost_distributions_json = models.JSONField(default=dict, blank=True)
    additional_costs_json = models.JSONField(default=dict, blank=True)
    profit_distributions_json = models.JSONField(default=dict, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['project', 'year', 'month']
        ordering = ['-year', '-month']
        verbose_name = _('Monthly Finance Report')
        verbose_name_plural = _('Monthly Finance Reports')

    def __str__(self):
        month_name = dict(self._meta.get_field('month').choices)[self.month]
        return f"{self.project.name} - {month_name} {self.year}"

    # Properties to access encrypted values
    @property
    def estimated_cost(self):
        return self.encrypted_estimated_cost

    @property
    def estimated_cost_usd(self):
        return self.encrypted_estimated_cost_usd

    @property
    def total_employee_cost(self):
        return self.encrypted_total_employee_cost

    @property
    def total_employee_cost_usd(self):
        return self.encrypted_total_employee_cost_usd

    @property
    def operational_cost(self):
        return self.encrypted_operational_cost

    @property
    def operational_cost_usd(self):
        return self.encrypted_operational_cost_usd

    @property
    def profit(self):
        return self.encrypted_profit

    @property
    def profit_usd(self):
        return self.encrypted_profit_usd

    @classmethod
    def generate_report(cls, project, year, month, finance_data):
        """
        Generate a monthly finance report from the provided data

        Args:
            project: Project instance
            year: Report year
            month: Report month (1-12)
            finance_data: Dictionary containing all finance data

        Returns:
            MonthlyFinanceReport instance
        """
        # Create or update the report
        report, created = cls.objects.update_or_create(
            project=project,
            year=year,
            month=month,
            defaults={
                # Exchange rate
                'exchange_rate': finance_data.get('exchange_rate'),
                'exchange_rate_date': finance_data.get('exchange_rate_date'),

                # Encrypted financial data
                'encrypted_estimated_cost': finance_data.get('estimated_cost', Decimal('0.00')),
                'encrypted_estimated_cost_usd': finance_data.get('estimated_cost_usd', Decimal('0.00')),
                'encrypted_total_employee_cost': finance_data.get('total_employee_cost', Decimal('0.00')),
                'encrypted_total_employee_cost_usd': finance_data.get('total_employee_cost_usd', Decimal('0.00')),
                'encrypted_operational_cost': finance_data.get('operational_cost', Decimal('0.00')),
                'encrypted_operational_cost_usd': finance_data.get('operational_cost_usd', Decimal('0.00')),
                'encrypted_profit': finance_data.get('profit', Decimal('0.00')),
                'encrypted_profit_usd': finance_data.get('profit_usd', Decimal('0.00')),

                # JSON data
                'employee_allocations_json': finance_data.get('employee_allocations', {}),
                'cost_distributions_json': finance_data.get('calculated_costs', {}),
                'additional_costs_json': finance_data.get('additional_costs', {}),
                'profit_distributions_json': finance_data.get('distributed_profits', {}),
            }
        )

        return report