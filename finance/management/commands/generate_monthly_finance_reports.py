import logging
import json
from datetime import datetime, date
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from project.models import Project
from finance.models import Finance, EmployeeAllocation, AdditionalCost, CostDistribution, ProfitDistribution, MonthlyFinanceReport
from finance.exchange_rate import ExchangeRateClient
from employee.models import EmployeeWorkInformation

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Generate monthly finance reports for all projects'

    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            help='Year for the report (defaults to current year)'
        )
        parser.add_argument(
            '--month',
            type=int,
            help='Month for the report (1-12, defaults to previous month)'
        )
        parser.add_argument(
            '--project_id',
            type=int,
            help='Generate report for a specific project only'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of reports even if they already exist'
        )

    def handle(self, *args, **options):
        # Determine the year and month to generate reports for
        today = timezone.now().date()

        # Default to previous month
        if today.month == 1:
            default_year = today.year - 1
            default_month = 12
        else:
            default_year = today.year
            default_month = today.month - 1

        year = options.get('year') or default_year
        month = options.get('month') or default_month
        project_id = options.get('project_id')
        force = options.get('force', False)

        # Validate month
        if month < 1 or month > 12:
            self.stderr.write(self.style.ERROR(f'Invalid month: {month}. Month must be between 1 and 12.'))
            return

        # Get projects to process
        if project_id:
            projects = Project.objects.filter(id=project_id)
            if not projects.exists():
                self.stderr.write(self.style.ERROR(f'Project with ID {project_id} not found.'))
                return
        else:
            projects = Project.objects.all()

        self.stdout.write(f'Generating finance reports for {month}/{year}')

        # Initialize exchange rate client
        client = ExchangeRateClient()
        latest_rate = client.get_latest_rate()

        # Process each project
        for project in projects:
            try:
                # Check if report already exists
                existing_report = MonthlyFinanceReport.objects.filter(
                    project=project,
                    year=year,
                    month=month
                ).first()

                if existing_report and not force:
                    self.stdout.write(f'Report for project {project.name} ({month}/{year}) already exists. Skipping.')
                    continue

                self.stdout.write(f'Generating report for project {project.name}')

                # Calculate exchange rate for INR to USD
                exchange_rate_inr_to_usd = None
                if latest_rate and 'rate' in latest_rate:
                    try:
                        exchange_rate_inr_to_usd = Decimal('1') / Decimal(str(latest_rate['rate']))
                    except (ZeroDivisionError, TypeError, ValueError):
                        pass

                # Calculate employee allocations
                employee_allocations = []
                total_employee_cost = Decimal(0)
                total_employee_cost_usd = Decimal(0)

                # Get allocations for this project
                try:
                    finance = Finance.objects.get(project=project)
                    allocations = EmployeeAllocation.objects.filter(finance=finance).select_related("employee")
                    allocation_map = {alloc.employee_id: alloc for alloc in allocations}

                    for employee in project.team_members.all():
                        try:
                            work_info = employee.employee_work_info
                            monthly_salary = Decimal(work_info.encrypted_basic_salary) if work_info and work_info.encrypted_basic_salary else Decimal(0)
                        except EmployeeWorkInformation.DoesNotExist:
                            monthly_salary = Decimal(0)

                        # Convert salary to USD if exchange rate is available
                        monthly_salary_usd = Decimal('0')
                        if exchange_rate_inr_to_usd and monthly_salary > 0:
                            monthly_salary_usd = monthly_salary * exchange_rate_inr_to_usd

                        allocation = allocation_map.get(employee.id)
                        percentage_allocation = Decimal(allocation.percentage_allocation) if allocation else Decimal(0)
                        allocated_cost = (percentage_allocation / Decimal(100)) * monthly_salary
                        allocated_cost_usd = Decimal('0')
                        if exchange_rate_inr_to_usd:
                            allocated_cost_usd = allocated_cost * exchange_rate_inr_to_usd

                        employee_allocations.append({
                            "employee_id": employee.id,
                            "employee_name": f"{employee.employee_first_name} {employee.employee_last_name}",
                            "monthly_salary": str(monthly_salary),
                            "monthly_salary_usd": str(monthly_salary_usd),
                            "percentage_allocation": str(percentage_allocation),
                            "allocated_cost": str(allocated_cost),
                            "allocated_cost_usd": str(allocated_cost_usd),
                        })

                        total_employee_cost += allocated_cost
                        total_employee_cost_usd += allocated_cost_usd
                except Finance.DoesNotExist:
                    pass

                # Get additional costs (these are already in USD)
                additional_costs = []
                total_additional_costs_usd = Decimal(0)
                total_additional_costs = Decimal(0)
                for cost in AdditionalCost.objects.filter(project=project):
                    # Additional costs are entered in USD, so cost.cost_value is already USD
                    cost_usd = cost.cost_value
                    # Convert USD to INR for INR calculations
                    cost_inr = Decimal('0')
                    if latest_rate and 'rate' in latest_rate:
                        cost_inr = cost_usd * Decimal(str(latest_rate['rate']))

                    additional_costs.append({
                        "cost_id": cost.id,
                        "cost_name": cost.cost_name,
                        "cost_value": str(cost_inr),  # INR value for backward compatibility
                        "cost_value_usd": str(cost_usd)  # USD value (original)
                    })
                    total_additional_costs += cost_inr  # INR total for calculations
                    total_additional_costs_usd += cost_usd  # USD total

                # The estimated cost is already in USD when entered during project creation
                estimated_cost_usd = project.encrypted_estimated_cost or Decimal('0')

                # Convert USD to INR for calculations
                estimated_cost = Decimal('0')
                if latest_rate and 'rate' in latest_rate and estimated_cost_usd > 0:
                    estimated_cost = estimated_cost_usd * Decimal(str(latest_rate['rate']))

                # Calculate cost distributions
                cost_distributions = []
                operational_cost = Decimal(0)
                operational_cost_usd = Decimal(0)

                for dist in CostDistribution.objects.filter(project=project):
                    calculated_value = (Decimal(dist.percentage) / Decimal(100)) * estimated_cost
                    calculated_value_usd = Decimal('0')
                    if exchange_rate_inr_to_usd:
                        calculated_value_usd = calculated_value * exchange_rate_inr_to_usd

                    cost_distributions.append({
                        "distribution_id": dist.id,
                        "category": dist.category,
                        "percentage": str(dist.percentage),
                        "calculated_value": str(calculated_value),
                        "calculated_value_usd": str(calculated_value_usd),
                    })

                    if dist.category.lower() in ["operation cost", "operational cost"]:
                        operational_cost = calculated_value
                        operational_cost_usd = calculated_value_usd

                # Calculate profit
                profit = operational_cost - total_employee_cost - total_additional_costs
                profit_usd = operational_cost_usd - total_employee_cost_usd - total_additional_costs_usd

                # Calculate profit distributions
                profit_distributions = []
                for dist in ProfitDistribution.objects.filter(project=project):
                    allocated_profit = dist.allocated_profit(profit)
                    allocated_profit_usd = Decimal('0')
                    if exchange_rate_inr_to_usd:
                        allocated_profit_usd = allocated_profit * exchange_rate_inr_to_usd

                    profit_distributions.append({
                        "distribution_id": dist.id,
                        "category": dist.category,
                        "percentage": str(dist.percentage),
                        "allocated_profit": str(allocated_profit),
                        "allocated_profit_usd": str(allocated_profit_usd),
                    })

                # Prepare finance data for report
                finance_data = {
                    # Exchange rate
                    'exchange_rate': latest_rate.get('rate') if latest_rate else None,
                    'exchange_rate_date': latest_rate.get('date') if latest_rate else None,

                    # Financial summary
                    'estimated_cost': estimated_cost,
                    'estimated_cost_usd': estimated_cost_usd,
                    'total_employee_cost': total_employee_cost,
                    'total_employee_cost_usd': total_employee_cost_usd,
                    'operational_cost': operational_cost,
                    'operational_cost_usd': operational_cost_usd,
                    'profit': profit,
                    'profit_usd': profit_usd,

                    # Detailed data
                    'employee_allocations': employee_allocations,
                    'calculated_costs': cost_distributions,
                    'additional_costs': additional_costs,
                    'distributed_profits': profit_distributions,
                }

                # Generate the report
                with transaction.atomic():
                    report = MonthlyFinanceReport.generate_report(
                        project=project,
                        year=year,
                        month=month,
                        finance_data=finance_data
                    )

                self.stdout.write(self.style.SUCCESS(f'Successfully generated report for {project.name} ({month}/{year})'))

            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error generating report for project {project.name}: {str(e)}'))
                logger.exception(f'Error generating report for project {project.id}')

        self.stdout.write(self.style.SUCCESS(f'Completed generating finance reports for {month}/{year}'))
