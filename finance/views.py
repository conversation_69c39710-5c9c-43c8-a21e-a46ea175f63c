from django.shortcuts import render, get_object_or_404, redirect
import os
from decimal import Decimal
from django.forms import modelformset_factory
from django.contrib.auth.decorators import login_required
from .decorators import finance_update_required
from employee.models import Employee
from .decorators import project_finance_list
from django.contrib import messages
from django.forms import modelformset_factory
from django.shortcuts import get_object_or_404, redirect, render
from .decorators import profit_distribution_required
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .decorators import finance_view_required
from .forms import AdditionalCostForm , ProfitDistributionForm , EmployeeAllocationForm , CostDistributionForm , AdditionalCostForm
from project.models import Project
from .exchange_rate import ExchangeRateClient, ExchangeRate
from django.utils import timezone
from django.http import JsonResponse
from .models import AdditionalCost, Project, Finance , AdditionalCost
from .models import AdditionalCost, Project
from .models import Finance, CostDistribution, EmployeeAllocation,ProfitDistribution
from employee.models import EmployeeWorkInformation



CostDistributionFormSet = modelformset_factory(
    CostDistribution,
    form=CostDistributionForm,
    extra=0,
    can_delete=True
)
EmployeeAllocationFormSet = modelformset_factory(
    EmployeeAllocation,
    form=EmployeeAllocationForm,
    extra=0,
    can_delete=False
)
from django.contrib import messages
from django.utils.translation import gettext as _
from django.shortcuts import render, redirect
import logging
logger = logging.getLogger(__name__)
@login_required
@project_finance_list()
def finance_project_list(request):
    query = request.GET.get("q", "").strip()

    try:
        employee = Employee.objects.filter(employee_user_id=request.user).first()
        if not employee and not request.user.is_superuser:
            messages.error(request, _('Employee profile not found for current user.'))
            return redirect('home-page')

        # Check if user is a superuser or in the Director group
        is_director = request.user.groups.filter(name__icontains='Director').exists()

        if request.user.is_superuser or is_director:
            # Both superusers and directors can see all projects
            projects = Project.objects.filter(name__icontains=query) if query else Project.objects.all()
        else:
            if query:
                projects_as_member = Project.objects.filter(team_members=employee, name__icontains=query)
                projects_as_owner = Project.objects.filter(project_owner=employee, name__icontains=query)
            else:
                projects_as_member = Project.objects.filter(team_members=employee)
                projects_as_owner = Project.objects.filter(project_owner=employee)

            projects = projects_as_member.union(projects_as_owner)

        context = {
            "projects": projects,
            "query": query,
            "page_title": _('Finance Projects List'),
            "is_admin": request.user.is_superuser,
            "is_director": is_director,
            "is_project_owner": Project.objects.filter(project_owner=employee).exists() if employee else False
        }
        return render(request, "finance/finance_project_list.html", context)

    except Exception as e:
        logger.error(f"Error in finance_project_list: {e}")
        messages.error(request, _('An error occurred while fetching finance projects.'))
        return redirect('home-page')



@login_required
@finance_view_required()
def finance_dashboard(request, project_id):
    # Get the project
    project = get_object_or_404(Project, id=project_id)

    # Check if a specific month/year report is requested
    report_year = request.GET.get('year')
    report_month = request.GET.get('month')
    show_report = False
    monthly_report = None

    # Get the MonthlyFinanceReport model
    from finance.models import MonthlyFinanceReport

    # Get available monthly reports for this project
    available_reports = MonthlyFinanceReport.objects.filter(project=project).order_by('-year', '-month')[:6]

    # If specific month/year requested and it exists in reports, use that data
    if report_year and report_month:
        try:
            report_year = int(report_year)
            report_month = int(report_month)
            monthly_report = MonthlyFinanceReport.objects.filter(
                project=project,
                year=report_year,
                month=report_month
            ).first()

            if monthly_report:
                show_report = True
            else:
                # Report was requested but doesn't exist
                messages.info(request, f"Report for {report_month}/{report_year} does not exist. Showing current data instead.")
        except (ValueError, TypeError):
            pass

    if show_report and monthly_report:
        # Use data from the monthly report
        exchange_rate_inr_to_usd = None
        if monthly_report.exchange_rate:
            try:
                exchange_rate_inr_to_usd = Decimal('1') / monthly_report.exchange_rate
            except (ZeroDivisionError, TypeError, ValueError):
                pass

        # Process employee allocations from JSON
        employee_allocations = []
        for alloc_data in monthly_report.employee_allocations_json:
            employee = None
            try:
                employee_id = alloc_data.get('employee_id')
                if employee_id:
                    employee = Employee.objects.get(id=employee_id)
            except Employee.DoesNotExist:
                pass

            if employee:
                employee_allocations.append({
                    "employee": employee,
                    "monthly_salary": Decimal(alloc_data.get('monthly_salary', '0')),
                    "monthly_salary_usd": Decimal(alloc_data.get('monthly_salary_usd', '0')),
                    "percentage_allocation": Decimal(alloc_data.get('percentage_allocation', '0')),
                    "allocated_cost": Decimal(alloc_data.get('allocated_cost', '0')),
                    "allocated_cost_usd": Decimal(alloc_data.get('allocated_cost_usd', '0')),
                })

        # Process cost distributions from JSON
        calculated_costs = []
        for cost_data in monthly_report.cost_distributions_json:
            calculated_costs.append({
                "category": cost_data.get('category', ''),
                "percentage": Decimal(cost_data.get('percentage', '0')),
                "calculated_value": Decimal(cost_data.get('calculated_value', '0')),
                "calculated_value_usd": Decimal(cost_data.get('calculated_value_usd', '0')),
            })

        # Process additional costs from JSON
        additional_costs_with_usd = []
        for cost_data in monthly_report.additional_costs_json:
            try:
                cost_id = cost_data.get('cost_id')
                if cost_id:
                    cost = AdditionalCost.objects.get(id=cost_id)
                    additional_costs_with_usd.append({
                        "original": cost,
                        "cost_value_usd": Decimal(cost_data.get('cost_value_usd', '0'))
                    })
            except AdditionalCost.DoesNotExist:
                # If the cost no longer exists, create a placeholder
                additional_costs_with_usd.append({
                    "original": {
                        "cost_name": cost_data.get('cost_name', 'Unknown'),
                        "cost_value": Decimal(cost_data.get('cost_value', '0'))
                    },
                    "cost_value_usd": Decimal(cost_data.get('cost_value_usd', '0'))
                })

        # Process profit distributions from JSON
        distributed_profits = []
        for profit_data in monthly_report.profit_distributions_json:
            distributed_profits.append({
                "category": profit_data.get('category', ''),
                "percentage": Decimal(profit_data.get('percentage', '0')),
                "allocated_profit": Decimal(profit_data.get('allocated_profit', '0')),
                "allocated_profit_usd": Decimal(profit_data.get('allocated_profit_usd', '0')),
            })

        # Prepare context with report data
        context = {
            "project": project,
            "employee_allocations": employee_allocations,
            "total_employee_cost": monthly_report.total_employee_cost,
            "total_employee_cost_usd": monthly_report.total_employee_cost_usd,
            "estimated_cost": monthly_report.estimated_cost,
            "estimated_cost_usd": monthly_report.estimated_cost_usd,
            "calculated_costs": calculated_costs,
            "operational_cost": monthly_report.operational_cost,
            "operational_cost_usd": monthly_report.operational_cost_usd,
            "profit": monthly_report.profit,
            "profit_usd": monthly_report.profit_usd,
            "distributed_profits": distributed_profits,
            "additional_costs": additional_costs_with_usd,
            "exchange_rate": monthly_report.exchange_rate,
            "exchange_rate_date": monthly_report.exchange_rate_date,
            "show_usd": exchange_rate_inr_to_usd is not None,
            "is_historical_report": True,
            "report_month": report_month,
            "report_year": report_year,
            "available_reports": available_reports,
        }
    else:
        # Calculate current data as before
        # Force refresh exchange rate data when loading dashboard
        api_key = os.getenv("EXCHANGE_RATE_API_KEY")
        client = ExchangeRateClient(api_key=api_key)  # Use your API key
        latest_rate = client.fetch_latest_rate()  # Force fetch from API instead of cache
        # Calculate exchange rate for INR to USD
        exchange_rate_inr_to_usd = None
        if latest_rate and 'rate' in latest_rate:
            try:
                exchange_rate_inr_to_usd = Decimal('1') / Decimal(str(latest_rate['rate']))
            except (ZeroDivisionError, TypeError, ValueError):
                pass
        employee_allocations = []
        additional_costs = AdditionalCost.objects.filter(project=project)
        total_additional_costs = sum(cost.cost_value for cost in additional_costs)
        total_employee_cost = Decimal(0)
        total_employee_cost_usd = Decimal(0)
        allocations = EmployeeAllocation.objects.filter(finance__project=project).select_related("employee")
        allocation_map = {alloc.employee_id: alloc for alloc in allocations}
        for employee in project.team_members.all():
            try:
                work_info = employee.employee_work_info
                monthly_salary = Decimal(work_info.encrypted_basic_salary) if work_info and work_info.encrypted_basic_salary else Decimal(0)
            except EmployeeWorkInformation.DoesNotExist:
                monthly_salary = Decimal(0)
            # Convert salary to USD if exchange rate is available
            monthly_salary_usd = Decimal('0')
            if exchange_rate_inr_to_usd and monthly_salary > 0:
                monthly_salary_usd = monthly_salary * exchange_rate_inr_to_usd
            allocation = allocation_map.get(employee.id)
            percentage_allocation = Decimal(allocation.percentage_allocation) if allocation else Decimal(0)
            allocated_cost = (percentage_allocation / Decimal(100)) * monthly_salary
            allocated_cost_usd = Decimal('0')
            if exchange_rate_inr_to_usd:
                allocated_cost_usd = allocated_cost * exchange_rate_inr_to_usd
            employee_allocations.append({
                "employee": employee,
                "monthly_salary": monthly_salary,
                "monthly_salary_usd": monthly_salary_usd,
                "percentage_allocation": percentage_allocation,
                "allocated_cost": allocated_cost,
                "allocated_cost_usd": allocated_cost_usd,
            })
            total_employee_cost += allocated_cost
            total_employee_cost_usd += allocated_cost_usd
        # The estimated cost is already in USD when entered during project creation
        estimated_cost_usd = project.encrypted_estimated_cost or Decimal('0')
        # Convert USD to INR for calculations
        estimated_cost = Decimal('0')
        if latest_rate and 'rate' in latest_rate and estimated_cost_usd > 0:
            estimated_cost = estimated_cost_usd * Decimal(str(latest_rate['rate']))
        cost_distributions = CostDistribution.objects.filter(project=project).only("category", "percentage")
        calculated_costs = []
        operational_cost = Decimal(0)
        operational_cost_usd = Decimal(0)
        for dist in cost_distributions:
            calculated_value = (Decimal(dist.percentage) / Decimal(100)) * estimated_cost
            calculated_value_usd = Decimal('0')
            if exchange_rate_inr_to_usd:
                calculated_value_usd = calculated_value * exchange_rate_inr_to_usd
            calculated_costs.append({
                "category": dist.category,
                "percentage": dist.percentage,
                "calculated_value": calculated_value,
                "calculated_value_usd": calculated_value_usd,
            })
            if dist.category.lower() in ["operation cost", "operational cost"]:
                operational_cost = calculated_value
                operational_cost_usd = calculated_value_usd
        profit = operational_cost - total_employee_cost - total_additional_costs
        profit_usd = Decimal('0')
        if exchange_rate_inr_to_usd:
            profit_usd = profit * exchange_rate_inr_to_usd
        profit_distributions = ProfitDistribution.objects.filter(project=project).only("category", "percentage")
        distributed_profits = []
        for dist in profit_distributions:
            allocated_profit = dist.allocated_profit(profit)
            allocated_profit_usd = Decimal('0')
            if exchange_rate_inr_to_usd:
                allocated_profit_usd = allocated_profit * exchange_rate_inr_to_usd
            distributed_profits.append({
                "category": dist.category,
                "percentage": dist.percentage,
                "allocated_profit": allocated_profit,
                "allocated_profit_usd": allocated_profit_usd,
            })
        # Additional costs are now entered in USD, convert to INR for display
        additional_costs_with_usd = []
        for cost in additional_costs:
            # cost.cost_value is already in USD
            cost_usd = cost.cost_value
            # Convert USD to INR for INR display
            cost_inr = Decimal('0')
            if latest_rate and 'rate' in latest_rate:
                cost_inr = cost_usd * Decimal(str(latest_rate['rate']))
            additional_costs_with_usd.append({
                "original": cost,
                "cost_value_usd": cost_inr  # This is actually INR value for backward compatibility
            })
        context = {
            "project": project,
            "employee_allocations": employee_allocations,
            "total_employee_cost": total_employee_cost,
            "total_employee_cost_usd": total_employee_cost_usd,
            "estimated_cost": estimated_cost,
            "estimated_cost_usd": estimated_cost_usd,
            "calculated_costs": calculated_costs,
            "operational_cost": operational_cost,
            "operational_cost_usd": operational_cost_usd,
            "profit": profit,
            "profit_usd": profit_usd,
            "distributed_profits": distributed_profits,
            "additional_costs": additional_costs_with_usd,
            "exchange_rate": latest_rate.get('rate') if latest_rate else None,
            "exchange_rate_date": latest_rate.get('date') if latest_rate else None,
            "show_usd": exchange_rate_inr_to_usd is not None,
            "is_historical_report": False,
            "available_reports": available_reports,
        }

    return render(request, "finance/finance_dashboard.html", context)

@login_required
@finance_update_required()
def finance_project_edit(request, project_id):
    project = get_object_or_404(Project, id=project_id)
    finance = Finance.objects.get_or_create(project=project)[0]
    project_employees = Employee.objects.filter(assigned_projects=project)
    EmployeeAllocation.objects.filter(finance=finance).exclude(employee__in=project_employees).delete()
    for employee in project_employees:
        EmployeeAllocation.objects.get_or_create(finance=finance, employee=employee)
    ea_qs = EmployeeAllocation.objects.filter(finance=finance)
    cd_qs = CostDistribution.objects.filter(project=project)

    if request.method == 'POST':
        cd_formset = CostDistributionFormSet(request.POST, queryset=cd_qs, prefix='cd')
        ea_formset = EmployeeAllocationFormSet(request.POST, queryset=ea_qs, prefix='ea')

        if cd_formset.is_valid() and ea_formset.is_valid():
            total_percentage = sum(
                form.cleaned_data.get('percentage', 0)
                for form in cd_formset
                if form.cleaned_data and not form.cleaned_data.get('DELETE', False)
            )
            if total_percentage != 100:
                messages.error(request, f"Total cost distribution must equal 100%. Current total is {total_percentage:.2f}%.")
            else:
                for form in cd_formset:
                    if form.cleaned_data:
                        if form.cleaned_data.get('DELETE', False) and form.instance.pk:
                            form.instance.delete()
                        else:
                            instance = form.save(commit=False)
                            instance.project = project
                            instance.save()
                for form in ea_formset:
                    if form.cleaned_data:
                        instance = form.save(commit=False)
                        # Get estimated cost in USD
                        estimated_cost_usd = project.encrypted_estimated_cost or Decimal('0')
                        # Convert to INR for allocation calculations
                        api_key = os.getenv("EXCHANGE_RATE_API_KEY")
                        client = ExchangeRateClient(api_key=api_key)
                        latest_rate = client.get_latest_rate()
                        estimated_cost = estimated_cost_usd
                        if latest_rate and 'rate' in latest_rate and estimated_cost_usd > 0:
                            estimated_cost = estimated_cost_usd * Decimal(str(latest_rate['rate']))
                        instance.allocated_cost = (
                            (Decimal(instance.percentage_allocation) / Decimal(100)) * estimated_cost
                        )
                        instance.save()
                messages.success(request, "Cost distribution and employee allocations updated successfully.")
                return redirect('finance_dashboard', project_id=project.id)
        else:
            messages.error(request, "There was an error updating the forms. Please check your inputs.")
    cd_formset = CostDistributionFormSet(queryset=cd_qs, prefix='cd')
    ea_formset = EmployeeAllocationFormSet(queryset=ea_qs, prefix='ea')
    context = {
        'project': project,
        'cd_formset': cd_formset,
        'ea_formset': ea_formset,
    }
    return render(request, 'finance/project_edit.html', context)


@login_required
@profit_distribution_required()
def profit_distribution_view(request, project_id):
    finance = get_object_or_404(Finance.objects.select_related("project"), project__id=project_id)
    project = get_object_or_404(Project.objects.prefetch_related("team_members__employee_work_info"), id=project_id)

    # Get exchange rate first
    api_key = os.getenv("EXCHANGE_RATE_API_KEY")
    client = ExchangeRateClient(api_key=api_key)
    latest_rate = client.get_latest_rate()

    additional_costs = AdditionalCost.objects.filter(project=project)
    # Additional costs are now in USD, convert to INR for calculations
    total_additional_costs = Decimal(0)
    for cost in additional_costs:
        # cost.cost_value is in USD, convert to INR
        cost_inr = cost.cost_value
        if latest_rate and 'rate' in latest_rate:
            cost_inr = cost.cost_value * Decimal(str(latest_rate['rate']))
        total_additional_costs += cost_inr

    employee_allocations = []
    total_employee_cost = Decimal(0)
    allocations = EmployeeAllocation.objects.filter(finance__project=project).select_related("employee")
    allocation_map = {alloc.employee_id: alloc for alloc in allocations}

    for employee in project.team_members.all():
        try:
            work_info = employee.employee_work_info
            monthly_salary = Decimal(work_info.encrypted_basic_salary) if work_info and work_info.encrypted_basic_salary else Decimal(0)
        except EmployeeWorkInformation.DoesNotExist:
            monthly_salary = Decimal(0)

        allocation = allocation_map.get(employee.id)
        percentage_allocation = Decimal(allocation.percentage_allocation) if allocation else Decimal(0)
        allocated_cost = (percentage_allocation / Decimal(100)) * monthly_salary

        employee_allocations.append({
            "employee": employee,
            "monthly_salary": monthly_salary,
            "percentage_allocation": percentage_allocation,
            "allocated_cost": allocated_cost,
        })
        total_employee_cost += allocated_cost

    # Get estimated cost in USD
    estimated_cost_usd = project.encrypted_estimated_cost or Decimal('0')
    # Convert to INR for calculations
    estimated_cost = estimated_cost_usd
    if latest_rate and 'rate' in latest_rate and estimated_cost_usd > 0:
        estimated_cost = estimated_cost_usd * Decimal(str(latest_rate['rate']))

    cost_distributions = CostDistribution.objects.filter(project=project).only("category", "percentage")
    calculated_costs = []
    operational_cost = Decimal(0)

    for dist in cost_distributions:
        calculated_value = (Decimal(dist.percentage) / Decimal(100)) * estimated_cost
        calculated_costs.append({
            "category": dist.category,
            "percentage": dist.percentage,
            "calculated_value": calculated_value,
        })
        if dist.category.lower() in ["operation cost", "operational cost"]:
            operational_cost = calculated_value

    total_profit = operational_cost - total_employee_cost - total_additional_costs
    if total_profit <= 0:
        messages.error(request, "Cannot manage profit distribution because the project is in loss.")
        return redirect("finance_dashboard", project_id=project_id)

    ProfitFormSet = modelformset_factory(ProfitDistribution, form=ProfitDistributionForm, extra=1, can_delete=True)
    formset = ProfitFormSet(queryset=ProfitDistribution.objects.filter(project=finance.project).only("category", "percentage"))

    if request.method == "POST":
        formset = ProfitFormSet(request.POST)
        if formset.is_valid():
            total_percentage = sum(
                form.cleaned_data["percentage"] for form in formset if form.cleaned_data and not form.cleaned_data.get("DELETE")
            )
            if total_percentage != 100:
                messages.error(request, "Total profit distribution must be 100%.")
            else:
                instances = formset.save(commit=False)
                for instance in instances:
                    instance.project = finance.project
                    instance.save()
                for obj in formset.deleted_objects:
                    obj.delete()
                messages.success(request, "Profit distribution updated successfully.")
                return redirect("finance_dashboard", project_id=project_id)

    return render(request, "finance/profit_distribution.html", {
        "formset": formset,
        "total_profit": total_profit,
        "finance": finance
    })


@login_required
def add_additional_cost(request, project_id):
    project = get_object_or_404(Project, id=project_id)

    AdditionalCostFormSet = modelformset_factory(
        AdditionalCost, form=AdditionalCostForm, extra=1, can_delete=True
    )

    additional_costs = AdditionalCost.objects.filter(project=project)

    if request.method == "POST":
        formset = AdditionalCostFormSet(request.POST, queryset=additional_costs)
        if formset.is_valid():
            instances = formset.save(commit=False)
            for instance in instances:
                instance.project = project
                instance.save()
            for obj in formset.deleted_objects:
                obj.delete()
            messages.success(request, "Additional costs updated successfully.")
            return redirect("finance_dashboard", project_id=project_id)
        else:
            print(formset.errors)

    else:
        formset = AdditionalCostFormSet(queryset=additional_costs)

    return render(request, "finance/additional_costs.html", {
        "formset": formset,
        "project": project,
    })

from django.shortcuts import render
def finance_reports(request):
    from .models import FinanceReport  # Move import inside the function
    reports = FinanceReport.objects.all()
    return render(request, "finance/reports.html", {"reports": reports})

from datetime import date
@login_required
def exchange_rates_view(request):
    from finance.models import ExchangeRate as ExchangeRateModel
    client = ExchangeRateClient()

    # Force update if requested
    force_update = request.GET.get('update') == '1'

    if force_update:
        latest_rate = client.fetch_latest_rate()
        messages.success(request, "Exchange rates updated successfully.")
    else:
        # Get the latest rate from database
        latest_db_rate = ExchangeRateModel.objects.order_by('-date').first()
        if latest_db_rate:
            latest_rate = {
                'base_currency': latest_db_rate.base_currency,
                'target_currency': latest_db_rate.target_currency,
                'rate': latest_db_rate.rate,
                'date': latest_db_rate.date
            }
        else:
            # If no rate in database, fetch from API
            latest_rate = client.fetch_latest_rate()

    # Get historical rates from database
    historical_db_rates = ExchangeRateModel.objects.all().order_by('-date')[:30]
    historical_rates = []

    for rate in historical_db_rates:
        historical_rates.append({
            'base_currency': rate.base_currency,
            'target_currency': rate.target_currency,
            'rate': rate.rate,
            'date': rate.date
        })

    # Calculate reverse rate for latest rate
    reverse_rate = None
    if latest_rate and 'rate' in latest_rate:
        try:
            reverse_rate = Decimal('1') / Decimal(str(latest_rate['rate']))
        except (ValueError, TypeError, ZeroDivisionError):
            reverse_rate = None

    # Calculate reverse rates for all historical rates
    reverse_rates = {}
    for rate_data in historical_rates:
        try:
            if 'rate' in rate_data:
                key = rate_data['date']
                if hasattr(key, 'isoformat'):
                    key = key.isoformat()
                reverse_rates[key] = Decimal('1') / Decimal(str(rate_data['rate']))
        except (ValueError, TypeError, ZeroDivisionError):
            continue

    context = {
        'latest_rate': latest_rate,
        'historical_rates': historical_rates,
        'reverse_rate': reverse_rate,
        'reverse_rates': reverse_rates,
        'today': date.today().isoformat(),
    }

    return render(request, "finance/exchange_rates.html", context)


@login_required
def convert_currency(request):
    """View to handle currency conversion requests"""
    if request.method == 'GET':
        try:
            from finance.models import ExchangeRate as ExchangeRateModel

            amount = request.GET.get('amount')
            from_currency = request.GET.get('from')
            to_currency = request.GET.get('to')

            # Debug prints to server console
            logger.info(f"Converting {amount} from {from_currency} to {to_currency}")

            if not amount or not from_currency or not to_currency:
                return JsonResponse({'success': False, 'error': 'Missing parameters'})

            # Convert amount to Decimal
            try:
                amount = Decimal(str(amount))
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Invalid amount: {str(e)}'})

            # Get the latest exchange rate from the database
            latest_rate = ExchangeRateModel.objects.order_by('-date').first()

            if not latest_rate:
                # If no rate in database, fetch from API
                client = ExchangeRateClient()
                api_rate = client.fetch_latest_rate()
                if api_rate and 'rate' in api_rate:
                    rate_value = Decimal(str(api_rate['rate']))
                else:
                    return JsonResponse({'success': False, 'error': 'No exchange rate available'})
            else:
                rate_value = latest_rate.rate

            # Perform conversion
            if from_currency == 'USD' and to_currency == 'INR':
                result = amount * rate_value
                return JsonResponse({'success': True, 'result': float(result)})
            elif from_currency == 'INR' and to_currency == 'USD':
                result = amount / rate_value
                return JsonResponse({'success': True, 'result': float(result)})

            return JsonResponse({'success': False, 'error': 'Conversion failed'})
        except Exception as e:
            logger.error(f"Conversion error: {str(e)}")
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})