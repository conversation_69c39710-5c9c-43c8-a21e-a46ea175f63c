# Set "DEBUG=False" for production
DEBUG=True

# Get a secure secret key from https://djecrety.ir
SECRET_KEY=django-insecure-j8op9)1q8$1&0^s&p*_0%d#pr@w9qj@1o=3#@d=a(^@9@zd@%j

# Don't use "*" for ALLOWED_HOSTS in production
ALLOWED_HOSTS=www.example.com,example.com,*

CSRF_TRUSTED_ORIGINS=

TIME_ZONE=Asia/Kolkata

# Database URL
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# ----OR----

# Database Configuration
DB_INIT_PASSWORD=d3f6a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d
DB_ENGINE=django.db.backends.postgresql
DB_NAME=dbname
DB_USER=user
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Razorpay Integration
RAZORPAY_ID=your-razorpay-id-here
RAZORPAY_KEY=your-razorpay-key-here

#EMAIL
EMAIL_HOST_USER=<email-address>
EMAIL_HOST_PASSWORD=<email-password>

#QuickBooks
QUICKBOOKS_ENV=sandbox
QUICKBOOKS_CLIENT_ID=""
QUICKBOOKS_CLIENT_SECRET=""
QUICKBOOKS_REDIRECT_URI=""
QUICKBOOKS_REALMID=""
QUICKBOOKS_APP_ID=""

# GCP Storage for Media Configuration
# GOOGLE_APPLICATION_CREDENTIALS="pathToYourServiceAccountJsonFile"
# GS_BUCKET_NAME="yourGCPBucketName"
# DEFAULT_FILE_STORAGE="horilla.horilla_backends_gcp.PrivateMediaStorage"
# MEDIA_URL="https://storage.cloud.google.com/yourGCPBucketName/media"
# MEDIA_ROOT="https://storage.cloud.google.com/yourGCPBucketName/media"
# NAMESPACE="private"



# Supportted Formats for DATABASE_URL :

# PostgreSQL: ``postgres[ql]?://`` or ``p[g]?sql://``
# PostGIS: ``postgis://``
# MySQL: ``mysql://`` or ``mysql2://``
# MySQL (GIS): ``mysqlgis://``
# MySQL Connector Python from Oracle: ``mysql-connector://``
# SQLite: ``sqlite://``
# SQLite with SpatiaLite for GeoDjango: ``spatialite://``
# Oracle: ``oracle://``
# Microsoft SQL Server: ``mssql://``
# PyODBC: ``pyodbc://``
# Amazon Redshift: ``redshift://``
# LDAP: ``ldap://``
