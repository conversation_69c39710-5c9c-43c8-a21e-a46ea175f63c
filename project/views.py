from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import PermissionDenied
from django import forms
from django.http import JsonResponse
from django.conf import settings
from django.db.models import Max
import json
import logging
from datetime import datetime, date
from calendar import monthrange
from django.contrib.auth.models import Group
from .models import Project
from .models_reports import MonthlyInvoiceReport
from .report_utils import (
    get_project_monthly_data, generate_monthly_report, generate_reports_for_all_projects,
    fetch_invoices_for_project, format_invoice_for_display, get_quickbooks_client
)
from employee.models import Employee
from .decorators import project_access_required, project_create_required, project_update_required, delete_permission, project_view_required
from django_select2.forms import Select2MultipleWidget, Select2Widget
from quickbooks.token_manager import TokenManager
from quickbooks.quickbooks_client import QuickBooksClient

# Set up logging
logger = logging.getLogger(__name__)


class ProjectForm(forms.ModelForm):
    team_members = forms.ModelMultipleChoiceField(
        queryset=Employee.objects.none(),  # Start with empty queryset that will be populated in __init__
        widget=Select2MultipleWidget(attrs={
            'class': 'form-control',
            'data-placeholder': 'Search and select team members'
        }),
        required=False
    )

    project_owner = forms.ModelChoiceField(
        queryset=Employee.objects.none(),
        widget=Select2Widget(attrs={
            'class': 'form-control',
            'data-placeholder': 'Search and select project owner'
        }),
        required=True
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super(ProjectForm, self).__init__(*args, **kwargs)

        if user:
            try:
                employee = Employee.objects.filter(employee_user_id=user).first()
                if employee and hasattr(employee, 'employee_work_info') and employee.employee_work_info.company_id:
                    company_id = employee.employee_work_info.company_id
                    company_employees = Employee.objects.filter(employee_work_info__company_id=company_id)
                    self.fields['team_members'].queryset = company_employees
                    self.fields['project_owner'].queryset = company_employees.filter(
                        employee_user_id__groups__name__icontains='Director'
                    ).distinct()
                    print("Filtered project_owner queryset:", self.fields['project_owner'].queryset)
                elif user.is_superuser:
                    all_employees = Employee.objects.all()
                    self.fields['team_members'].queryset = all_employees
                    self.fields['project_owner'].queryset = all_employees.filter(
                        employee_user_id__groups__name__icontains='Director'
                    ).distinct()
                else:
                    logger.warning(f"Employee has no company_id. User: {user.username}, Employee: {employee}")
                    all_employees = Employee.objects.all()
                    self.fields['team_members'].queryset = all_employees
                    self.fields['project_owner'].queryset = all_employees
            except Exception as e:
                logger.error(f"Error setting employee querysets: {e}")
                all_employees = Employee.objects.all()
                self.fields['team_members'].queryset = all_employees
                self.fields['project_owner'].queryset = all_employees
        else:
            all_employees = Employee.objects.all()
            self.fields['team_members'].queryset = all_employees
            self.fields['project_owner'].queryset = all_employees

    class Meta:
        model = Project
        fields = [
            'name', 'start_date', 'end_date', 'attachment', 'description1',
            'status', 'project_owner', 'team_members', 'encrypted_estimated_cost', 'encrypted_open_balance',
            'project_type'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Project Name',
                'required': True
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control',
                'required': True
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'description1': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Project Description',
                'required': True
            }),
            'status': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'project_owner': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'encrypted_estimated_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Estimated Cost',
                'required': True
            }),
            'project_type': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
        }


# List Projects
@login_required
@project_view_required('project.view_project')
def project_list(request):
    query = request.GET.get("q", "").strip()
    owner_id = request.GET.get("owner", "").strip()  # Get owner filter parameter

    # Initialize TokenManager and check if QuickBooks is connected
    manager = TokenManager()
    # Load tokens from cache
    manager._load_tokens_from_cache()
    # Check if we have valid tokens
    qb_connected = False
    if manager.access_token and manager.refresh_token:
        try:
            qb_connected = not manager.is_expired(auto_refresh=True)
        except Exception as e:
            logger.error(f"Error checking token expiry: {e}")

    try:
        employee = Employee.objects.filter(employee_user_id=request.user).first()

        if not employee and not request.user.is_superuser:
            messages.error(request, _("Employee profile not found for current user."))
            return redirect("home-page")

        # Get all possible project owners for the filter dropdown
        all_owners = Employee.objects.filter(
            id__in=Project.objects.values_list('project_owner', flat=True).distinct()
        )

        # First filter by user access
        # Check if user is a superuser or in the Director group
        is_director = request.user.groups.filter(name__icontains='Director').exists()

        if request.user.is_superuser or is_director:
            # Both superusers and directors can see all projects
            projects = Project.objects.all()
        else:
            projects_as_member = Project.objects.filter(team_members=employee)
            projects_as_owner = Project.objects.filter(project_owner=employee)
            projects = projects_as_member.union(projects_as_owner)

        # Then apply owner filter if provided
        if owner_id:
            try:
                projects = projects.filter(project_owner_id=int(owner_id))
            except (ValueError, TypeError):
                # If owner_id is not a valid integer, ignore the filter
                pass

        # Finally apply search filter if provided
        if query:
            projects = projects.filter(name__icontains=query)

        context = {
            "projects": projects,
            "query": query,
            "all_owners": all_owners,
            "selected_owner": owner_id,
            "page_title": _("Projects List"),
            "is_admin": request.user.is_superuser,
            "is_director": is_director,
            "is_project_owner": Project.objects.filter(project_owner=employee).exists() if employee else False,
            "qb_connected": qb_connected,
            "hrms_base_url": settings.HRMS_BASE_URL,
        }
        return render(request, "project/project_list.html", context)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"Error in project_list: {e}")
        messages.error(request, _("An error occurred while fetching projects."))
        return redirect("project:project_list")    # View Project Details


@login_required
@project_access_required("project.view_project")
def project_detail(request, pk):
    project = get_object_or_404(Project, pk=pk)

    try:
        profit_margin = project.get_profit_margin() if hasattr(project, 'get_profit_margin') else None
    except Exception as e:
        logger.error(f"Error calculating profit margin: {e}")
        profit_margin = None

    context = {
        'url': project.signed_url,
        'project': project,
        'page_title': f"Project: {project.name}",
        'profit_margin': profit_margin,
        'is_admin': request.user.is_superuser,
    }
    return render(request, 'project/project_detail.html', context)


# Create Project
@login_required
@project_create_required()
def project_create(request):
    if request.method == 'POST':
        form = ProjectForm(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            project = form.save(commit=False)
            project.attachment = request.FILES.get('attachment', None)
            project.save()
            form.save_m2m()
            messages.success(request, _('Project created successfully'))
            return redirect('project:project_detail', pk=project.pk)
        else:
            logger.error(f"Project Form Errors: {form.errors}")
            messages.error(request, _('Please fill all the required fields correctly.'))
    else:
        form = ProjectForm(user=request.user)

    return render(request, 'project/project_form.html', {
        'form': form,
        'is_admin': True
    })


# Update Project
@login_required
@project_update_required()
def project_update(request, pk):
    project = get_object_or_404(Project, pk=pk)
    # Store the current encrypted_open_balance value
    current_open_balance = project.encrypted_open_balance

    if request.method == 'POST':
        form = ProjectForm(request.POST, request.FILES, instance=project, user=request.user)
        if form.is_valid():
            if not request.FILES.get('attachment'):
                form.instance.attachment = project.attachment
            form.instance.encrypted_open_balance = current_open_balance
            project = form.save(commit=False)
            project.save()
            form.save_m2m()
            messages.success(request, _('Project updated successfully'))
            return redirect('project:project_detail', pk=project.pk)
        else:
            logger.error(f"Project Update Form Errors: {form.errors}")
            messages.error(request, _('Please fill all the required fields correctly.'))
    else:
        form = ProjectForm(instance=project, user=request.user)

    return render(request, 'project/project_form.html', {
        'form': form,
        'project': project,
        'page_title': _('Update Project'),
        'is_admin': True
    })


# Delete Project
@login_required
@delete_permission
def project_delete(request, pk):
    project = get_object_or_404(Project, pk=pk)

    if request.method == 'POST':
        project.delete()
        messages.success(request, _('Project deleted successfully'))
        return redirect('project:project_list')

    return render(request, 'project/project_confirm_delete.html', {
        'project': project,
        'page_title': _('Delete Project'),
        'is_admin': True
    })


@login_required
def sync_qb_customers(request):
    """
    Syncs all QuickBooks customers as projects.

    If a project with the same customer_id already exists, updates only the name and encrypted_estimated_cost fields.
    If it's a new customer_id, creates a new project.

    Returns a JSON response with the sync results.
    """
    # First filter by user access
    # Check if user is a superuser or in the Director group
    is_director = request.user.groups.filter(name__icontains='Director').exists()
    if not request.user.is_superuser and not is_director:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

        # Only allow POST requests
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Only POST requests are allowed'}, status=405)

    # Initialize the token manager
    token_manager = TokenManager()
    # Load tokens from cache
    token_manager._load_tokens_from_cache()

    # Check if we have valid tokens
    if not token_manager.access_token or not token_manager.refresh_token or token_manager.is_expired(auto_refresh=True):
        return JsonResponse({
            'status': 'error',
            'message': 'No valid QuickBooks tokens found. Please authenticate first.'
        }, status=400)

    # Initialize the QuickBooks client
    qb_client = QuickBooksClient(token_manager, settings.QUICKBOOKS_REALMID)

    # Fetch customers from QuickBooks
    try:
        customers = fetch_customers(qb_client)
    except Exception as e:
        logger.error(f"Error fetching customers: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'Error fetching customers from QuickBooks: {str(e)}'
        }, status=500)

    if not customers:
        return JsonResponse({
            'status': 'warning',
            'message': 'No customers found in QuickBooks'
        })

    # Process all customers, not just new ones
    new_customers = customers

    # Create or update projects for customers
    created_count = 0
    updated_count = 0
    for customer in new_customers:
        customer_name = customer.get('DisplayName', '')
        customer_id = customer.get('Id', '')
        balance = customer.get('Balance', 0)

        if not customer_name or not customer_id:
            continue

        # Check if a project with this customer_id already exists
        existing_project = Project.objects.filter(customer_id=customer_id).first()

        if existing_project:
            # Update existing project's name and encrypted_estimated_cost
            try:
                existing_project.name = customer_name
                existing_project.encrypted_open_balance = balance
                existing_project.save(update_fields=['name', 'encrypted_open_balance'])
                updated_count += 1
                logger.info(f"Updated project for customer: {customer_name} (ID: {customer_id})")
            except Exception as e:
                logger.error(f"Error updating project for customer {customer_name} (ID: {customer_id}): {e}")
        else:
            # Create a new project
            try:
                project = Project.objects.create(
                    name=customer_name,
                    start_date=datetime.now().date(),
                    status='planning',
                    description1=f'Project created from QuickBooks customer: {customer_name}',
                    encrypted_open_balance=balance,
                    project_type='fix_price',
                    company_id=settings.QUICKBOOKS_REALMID,
                    customer_id=customer_id
                )
                created_count += 1
                logger.info(f"Created project for customer: {customer_name} (ID: {customer_id})")
            except Exception as e:
                logger.error(f"Error creating project for customer {customer_name} (ID: {customer_id}): {e}")

    return JsonResponse({
        'status': 'success',
        'message': f'Successfully synced {created_count} new customers and updated {updated_count} existing projects from QuickBooks',
        'created_count': created_count,
        'updated_count': updated_count
    })


@login_required
def sync_single_project(request, pk):
    """
    Syncs a single project with its corresponding QuickBooks customer.
    Updates the project name and encrypted_estimated_cost (open balance) fields.

    Args:
        request: The HTTP request
        pk: The primary key of the project to sync

    Returns:
        JSON response with the sync result or redirects back to the project list
    """
    # First filter by user access
    # Check if user is a superuser or in the Director group
    is_director = request.user.groups.filter(name__icontains='Director').exists()
    if not request.user.is_superuser and not is_director:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    # Only allow POST requests
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Only POST requests are allowed'}, status=405)

    try:
        # Get the project
        project = get_object_or_404(Project, pk=pk)

        # Check if the project has a customer_id
        if not project.customer_id:
            return JsonResponse({
                'status': 'error',
                'message': 'This project is not linked to a QuickBooks customer'
            }, status=400)

        # Initialize the token manager
        token_manager = TokenManager()
        # Load tokens from cache
        token_manager._load_tokens_from_cache()

        # Check if we have valid tokens
        if not token_manager.access_token or not token_manager.refresh_token or token_manager.is_expired(auto_refresh=True):
            return JsonResponse({
                'status': 'error',
                'message': 'No valid QuickBooks tokens found. Please authenticate first.'
            }, status=400)

        # Initialize the QuickBooks client
        qb_client = QuickBooksClient(token_manager, settings.QUICKBOOKS_REALMID)

        # Fetch customers from QuickBooks
        try:
            customers = fetch_customers(qb_client)
        except Exception as e:
            logger.error(f"Error fetching customers: {e}")
            return JsonResponse({
                'status': 'error',
                'message': f'Error fetching customers from QuickBooks: {str(e)}'
            }, status=500)

        if not customers:
            return JsonResponse({
                'status': 'warning',
                'message': 'No customers found in QuickBooks'
            })

        # Find the customer with the matching ID
        customer = None
        for c in customers:
            if c.get('Id') == project.customer_id:
                customer = c
                break

        if not customer:
            return JsonResponse({
                'status': 'error',
                'message': f'Customer with ID {project.customer_id} not found in QuickBooks'
            }, status=404)

        # Get the customer name and balance
        customer_name = customer.get('DisplayName', '')
        balance = customer.get('Balance', 0)

        # Update the project
        old_name = project.name
        old_balance = project.encrypted_open_balance

        project.name = customer_name
        project.encrypted_open_balance = balance
        project.save(update_fields=['name', 'encrypted_open_balance'])

        # Log the changes
        changes = []
        if old_name != customer_name:
            changes.append(f"Name: {old_name} → {customer_name}")
        if old_balance != balance:
            changes.append(f"Open Balance: {old_balance} → {balance}")

        if changes:
            change_message = ", ".join(changes)
            return JsonResponse({
                'status': 'success',
                'message': f'Project updated: {change_message}',
                'project_id': project.id,
                'name': customer_name,
                'encrypted_open_balance': balance
            })
        else:
            return JsonResponse({
                'status': 'info',
                'message': 'No changes needed, project is already up to date',
                'project_id': project.id
            })

    except Project.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': f'Project with ID {pk} not found'
        }, status=404)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error in sync_single_project: {e}\n{error_details}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred: {str(e)}. Please check the logs for more details.'
        }, status=500)


def fetch_customers(qb_client):
    """
    Fetch customers from QuickBooks

    Args:
        qb_client: QuickBooksClient instance

    Returns:
        List of customer objects
    """
    try:
        customers = qb_client.get_customers()
        logger.info(f"Successfully fetched {len(customers)} customers from QuickBooks")
        return customers
    except ValueError as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Token error fetching customers: {e}\n{error_details}")
        return []
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error fetching customers: {e}\n{error_details}")
        return []


def fetch_invoices(qb_client, customer_id=None):
    """
    Fetch invoices from QuickBooks, optionally filtered by customer_id

    Args:
        qb_client: QuickBooksClient instance
        customer_id: Optional QuickBooks customer ID to filter invoices by

    Returns:
        List of invoice objects
    """
    try:
        invoices = qb_client.get_invoices(customer_id=customer_id)
        logger.info(f"Successfully fetched {len(invoices)} invoices from QuickBooks")
        return invoices
    except ValueError as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Token error fetching invoices: {e}\n{error_details}")
        return []
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error fetching invoices: {e}\n{error_details}")
        return []


@login_required
@project_access_required("project.view_project")
def project_monthly_report(request, pk):
    """
    Display monthly invoice report for a project

    Args:
        request: The HTTP request
        pk: The primary key of the project

    Returns:
        Rendered template with monthly report data
    """
    project = get_object_or_404(Project, pk=pk)

    # Get year and month from query parameters or use current date
    today = date.today()
    try:
        year = int(request.GET.get('year', today.year))
        month = int(request.GET.get('month', today.month))
        # Validate year and month
        if not (1900 <= year <= 2100) or not (1 <= month <= 12):
            year, month = today.year, today.month
    except (ValueError, TypeError):
        year, month = today.year, today.month

    # Get monthly data for the project
    monthly_data = get_project_monthly_data(project, year, month)

    # Get all available years and months for this project
    available_dates = MonthlyInvoiceReport.objects.filter(
        project=project
    ).values('year', 'month').distinct().order_by('-year', '-month')

    # Add current month if not in the list
    current_month_exists = any(
        date_info['year'] == year and date_info['month'] == month
        for date_info in available_dates
    )
    if not current_month_exists:
        available_dates = [{'year': year, 'month': month}] + list(available_dates)

    # Initialize TokenManager and check if QuickBooks is connected
    manager = TokenManager()
    # Check if we have valid tokens
    qb_connected = False
    if manager.access_token and manager.refresh_token:
        try:
            qb_connected = not manager.is_expired(auto_refresh=True)
        except Exception as e:
            logger.error(f"Error checking token expiry: {e}")

    context = {
        'project': project,
        'monthly_data': monthly_data,
        'available_dates': available_dates,
        'current_year': year,
        'current_month': month,
        'page_title': f"Monthly Invoice Report: {project.name}",
        'is_admin': request.user.is_superuser,
        'qb_connected': qb_connected,
    }

    return render(request, 'project/project_monthly_report.html', context)


@login_required
def generate_project_monthly_report(request, pk):
    """
    Generate monthly report for a specific project

    Args:
        request: The HTTP request
        pk: The primary key of the project

    Returns:
        JSON response with the generation results
    """
    if not request.user.is_superuser:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    # Only allow POST requests
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Only POST requests are allowed'}, status=405)

    # Initialize TokenManager and check if QuickBooks is connected
    manager = TokenManager()
    # Check if we have valid tokens
    if not manager.access_token or not manager.refresh_token:
        return JsonResponse({
            'status': 'error',
            'message': 'You need to connect to QuickBooks account first.'
        }, status=400)

    try:
        # Check if tokens are expired
        if manager.is_expired(auto_refresh=True):
            return JsonResponse({
                'status': 'error',
                'message': 'Your QuickBooks connection has expired. Please reconnect to QuickBooks.'
            }, status=400)

        # Get the project
        project = get_object_or_404(Project, pk=pk)

        # Get year and month from request or use current date
        today = date.today()
        try:
            year = int(request.POST.get('year', today.year))
            month = int(request.POST.get('month', today.month))
            # Validate year and month
            if not (1900 <= year <= 2100) or not (1 <= month <= 12):
                year, month = today.year, today.month
        except (ValueError, TypeError):
            year, month = today.year, today.month

        # Generate report for the specific project
        report = generate_monthly_report(project, year, month)

        if report:
            return JsonResponse({
                'status': 'success',
                'message': f'Successfully generated monthly report for project {project.name} for {month}/{year}',
                'project_id': project.id,
                'project_name': project.name
            })
        else:
            return JsonResponse({
                'status': 'warning',
                'message': f'No data available to generate report for project {project.name} for {month}/{year}',
                'project_id': project.id,
                'project_name': project.name
            })
    except Project.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': f'Project with ID {pk} not found'
        }, status=404)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error generating monthly report for project {pk}: {e}\n{error_details}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred: {str(e)}. Please check the logs for more details.'
        }, status=500)


@login_required
def fetch_project_invoices(request, pk):
    """
    Fetches invoices for a specific project from QuickBooks.

    Args:
        request: The HTTP request
        pk: The primary key of the project

    Returns:
        JSON response with the invoices or an error message
    """
    if not request.user.is_superuser:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    # Only allow POST requests
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Only POST requests are allowed'}, status=405)

    try:
        # Get the project
        project = get_object_or_404(Project, pk=pk)

        # Get year and month from request or use current date
        today = date.today()
        try:
            year = int(request.POST.get('year', today.year))
            month = int(request.POST.get('month', today.month))
            # Validate year and month
            if not (1900 <= year <= 2100) or not (1 <= month <= 12):
                year, month = today.year, today.month
        except (ValueError, TypeError):
            year, month = today.year, today.month

        # Generate report for the specific project
        report = generate_monthly_report(project, year, month)

        if report:
            return JsonResponse({
                'status': 'success',
                'message': f'Successfully generated monthly report for project {project.name} for {month}/{year}',
                'project_id': project.id,
                'project_name': project.name
            })
        else:
            return JsonResponse({
                'status': 'warning',
                'message': f'No data available to generate report for project {project.name} for {month}/{year}',
                'project_id': project.id,
                'project_name': project.name
            })
    except Project.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': f'Project with ID {pk} not found'
        }, status=404)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error generating monthly report for project {pk}: {e}\n{error_details}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred: {str(e)}. Please check the logs for more details.'
        }, status=500)


@login_required
def fetch_project_invoices(request, pk):
    """
    Fetches invoices for a specific project from QuickBooks.

    Args:
        request: The HTTP request
        pk: The primary key of the project

    Returns:
        JSON response with the invoices or an error message
    """
    # First filter by user access
    # Check if user is a superuser or in the Director group
    is_director = request.user.groups.filter(name__icontains='Director').exists()
    if not request.user.is_superuser and not is_director:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    try:
        # Get the project
        project = get_object_or_404(Project, pk=pk)

        # Check if the project has a customer_id
        if not project.customer_id:
            return JsonResponse({
                'status': 'error',
                'message': 'This project is not linked to a QuickBooks customer'
            }, status=400)

        # Fetch invoices directly using our utility function
        invoices = fetch_invoices_for_project(project)

        if not invoices:
            return JsonResponse({
                'status': 'warning',
                'message': f'No invoices found for customer ID {project.customer_id}'
            })

        # Format the invoices for the response using our utility function
        formatted_invoices = [format_invoice_for_display(invoice) for invoice in invoices]

        return JsonResponse({
            'status': 'success',
            'message': f'Successfully fetched {len(invoices)} invoices for project {project.name}',
            'project_name': project.name,
            'customer_id': project.customer_id,
            'invoices': formatted_invoices
        })

    except Project.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': f'Project with ID {pk} not found'
        }, status=404)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Error in fetch_project_invoices: {e}\n{error_details}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred: {str(e)}. Please check the logs for more details.'
        }, status=500)
